import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:novel_app/config/cloudbase_config.dart';
import 'package:novel_app/services/cloudbase_service.dart';

/// 腾讯云开发数据库服务类
/// 负责数据库的增删改查操作
class CloudBaseDatabaseService {
  static CloudBaseDatabaseService? _instance;
  static CloudBaseDatabaseService get instance => _instance ??= CloudBaseDatabaseService._();
  
  CloudBaseDatabaseService._();
  
  final _cloudBase = CloudBaseService.instance;
  
  /// 通用请求方法
  Future<Map<String, dynamic>?> _makeRequest(
    String endpoint, {
    Map<String, dynamic>? data,
    String method = 'POST',
  }) async {
    return await _cloudBase.makeRequest(endpoint, data: data, method: method);
  }

  /// 插入数据
  Future<Map<String, dynamic>?> insertData({
    required String collection,
    required Map<String, dynamic> data,
  }) async {
    try {
      final result = await _makeRequest(
        CloudBaseConfig.insertEndpoint,
        data: {
          'collection': collection,
          'data': data,
        },
      );

      debugPrint('数据插入成功: $collection');
      return result;
    } catch (e) {
      debugPrint('数据插入失败: $e');
      return null;
    }
  }
  
  /// 查询数据
  Future<List<Map<String, dynamic>>?> queryData({
    required String collection,
    Map<String, dynamic>? where,
    Map<String, dynamic>? orderBy,
    int? limit,
    int? skip,
  }) async {
    try {
      final queryData = <String, dynamic>{
        'collection': collection,
      };
      
      if (where != null) queryData['where'] = where;
      if (orderBy != null) queryData['orderBy'] = orderBy;
      if (limit != null) queryData['limit'] = limit;
      if (skip != null) queryData['skip'] = skip;
      
      final result = await _makeRequest(
        CloudBaseConfig.queryEndpoint,
        data: queryData,
      );

      if (result != null && result['data'] != null) {
        return List<Map<String, dynamic>>.from(result['data']);
      }
      
      return [];
    } catch (e) {
      debugPrint('数据查询失败: $e');
      return null;
    }
  }
  
  /// 更新数据
  Future<bool> updateData({
    required String collection,
    required Map<String, dynamic> where,
    required Map<String, dynamic> data,
  }) async {
    try {
      await _makeRequest(
        CloudBaseConfig.updateEndpoint,
        data: {
          'collection': collection,
          'where': where,
          'data': data,
        },
      );
      
      debugPrint('数据更新成功: $collection');
      return true;
    } catch (e) {
      debugPrint('数据更新失败: $e');
      return false;
    }
  }
  
  /// 删除数据
  Future<bool> deleteData({
    required String collection,
    required Map<String, dynamic> where,
  }) async {
    try {
      await _makeRequest(
        CloudBaseConfig.deleteEndpoint,
        data: {
          'collection': collection,
          'where': where,
        },
      );

      debugPrint('数据删除成功: $collection');
      return true;
    } catch (e) {
      debugPrint('数据删除失败: $e');
      return false;
    }
  }
  
  /// 保存用户设置
  Future<bool> saveUserSettings({
    required String userId,
    required Map<String, dynamic> settings,
  }) async {
    final data = {
      'userId': userId,
      'settings': settings,
      'updatedAt': DateTime.now().toIso8601String(),
    };
    
    // 先查询是否存在
    final existing = await queryData(
      collection: CloudBaseConfig.userSettingsCollection,
      where: {'userId': userId},
    );

    if (existing != null && existing.isNotEmpty) {
      // 更新现有记录
      return await updateData(
        collection: CloudBaseConfig.userSettingsCollection,
        where: {'userId': userId},
        data: data,
      );
    } else {
      // 插入新记录
      data['createdAt'] = DateTime.now().toIso8601String();
      final result = await insertData(
        collection: CloudBaseConfig.userSettingsCollection,
        data: data,
      );
      return result != null;
    }
  }
  
  /// 获取用户设置
  Future<Map<String, dynamic>?> getUserSettings(String userId) async {
    final results = await queryData(
      collection: CloudBaseConfig.userSettingsCollection,
      where: {'userId': userId},
      limit: 1,
    );
    
    if (results != null && results.isNotEmpty) {
      return results.first['settings'];
    }
    
    return null;
  }
  
  /// 保存用户收藏
  Future<bool> saveUserFavorites({
    required String userId,
    required List<Map<String, dynamic>> favorites,
  }) async {
    final data = {
      'userId': userId,
      'favorites': favorites,
      'updatedAt': DateTime.now().toIso8601String(),
    };

    // 先查询是否存在
    final existing = await queryData(
      collection: CloudBaseConfig.userFavoritesCollection,
      where: {'userId': userId},
    );

    if (existing != null && existing.isNotEmpty) {
      // 更新现有记录
      return await updateData(
        collection: CloudBaseConfig.userFavoritesCollection,
        where: {'userId': userId},
        data: data,
      );
    } else {
      // 插入新记录
      data['createdAt'] = DateTime.now().toIso8601String();
      final result = await insertData(
        collection: CloudBaseConfig.userFavoritesCollection,
        data: data,
      );
      return result != null;
    }
  }
  
  /// 获取用户收藏
  Future<List<Map<String, dynamic>>?> getUserFavorites(String userId) async {
    final results = await queryData(
      collection: CloudBaseConfig.userFavoritesCollection,
      where: {'userId': userId},
      limit: 1,
    );
    
    if (results != null && results.isNotEmpty) {
      final favorites = results.first['favorites'];
      if (favorites is List) {
        return List<Map<String, dynamic>>.from(favorites);
      }
    }
    
    return [];
  }
  
  /// 保存用户历史记录
  Future<bool> saveUserHistory({
    required String userId,
    required List<Map<String, dynamic>> history,
  }) async {
    final data = {
      'userId': userId,
      'history': history,
      'updatedAt': DateTime.now().toIso8601String(),
    };

    // 先查询是否存在
    final existing = await queryData(
      collection: CloudBaseConfig.userHistoryCollection,
      where: {'userId': userId},
    );

    if (existing != null && existing.isNotEmpty) {
      // 更新现有记录
      return await updateData(
        collection: CloudBaseConfig.userHistoryCollection,
        where: {'userId': userId},
        data: data,
      );
    } else {
      // 插入新记录
      data['createdAt'] = DateTime.now().toIso8601String();
      final result = await insertData(
        collection: CloudBaseConfig.userHistoryCollection,
        data: data,
      );
      return result != null;
    }
  }
  
  /// 获取用户历史记录
  Future<List<Map<String, dynamic>>?> getUserHistory(String userId) async {
    final results = await queryData(
      collection: CloudBaseConfig.userHistoryCollection,
      where: {'userId': userId},
      limit: 1,
    );
    
    if (results != null && results.isNotEmpty) {
      final history = results.first['history'];
      if (history is List) {
        return List<Map<String, dynamic>>.from(history);
      }
    }
    
    return [];
  }
  
  /// 保存会员专属数据
  Future<bool> saveMemberData({
    required String userId,
    required Map<String, dynamic> memberData,
  }) async {
    final data = {
      'userId': userId,
      'memberData': memberData,
      'updatedAt': DateTime.now().toIso8601String(),
    };

    // 先查询是否存在
    final existing = await queryData(
      collection: CloudBaseConfig.memberDataCollection,
      where: {'userId': userId},
    );

    if (existing != null && existing.isNotEmpty) {
      // 更新现有记录
      return await updateData(
        collection: CloudBaseConfig.memberDataCollection,
        where: {'userId': userId},
        data: data,
      );
    } else {
      // 插入新记录
      data['createdAt'] = DateTime.now().toIso8601String();
      final result = await insertData(
        collection: CloudBaseConfig.memberDataCollection,
        data: data,
      );
      return result != null;
    }
  }
  
  /// 获取会员专属数据
  Future<Map<String, dynamic>?> getMemberData(String userId) async {
    final results = await queryData(
      collection: CloudBaseConfig.memberDataCollection,
      where: {'userId': userId},
      limit: 1,
    );

    if (results != null && results.isNotEmpty) {
      return results.first['memberData'];
    }
    
    return null;
  }
  
  /// 清除用户所有数据
  Future<bool> clearUserData(String userId) async {
    try {
      // 删除用户设置
      await deleteData(
        collection: CloudBaseConfig.userSettingsCollection,
        where: {'userId': userId},
      );

      // 删除用户收藏
      await deleteData(
        collection: CloudBaseConfig.userFavoritesCollection,
        where: {'userId': userId},
      );

      // 删除用户历史记录
      await deleteData(
        collection: CloudBaseConfig.userHistoryCollection,
        where: {'userId': userId},
      );

      // 删除会员专属数据
      await deleteData(
        collection: CloudBaseConfig.memberDataCollection,
        where: {'userId': userId},
      );
      
      debugPrint('用户数据清除成功: $userId');
      return true;
    } catch (e) {
      debugPrint('用户数据清除失败: $e');
      return false;
    }
  }
}
