# 腾讯云开发集成配置指南

本指南将帮助您配置腾讯云开发（CloudBase）后端服务，为您的小说应用提供用户认证和数据同步功能。

## 第一步：创建腾讯云开发环境

### 1. 注册腾讯云账号
- 访问 [腾讯云官网](https://cloud.tencent.com/)
- 注册并完成实名认证

### 2. 开通云开发服务
- 访问 [腾讯云开发控制台](https://tcb.cloud.tencent.com/)
- 点击"新建环境"
- 选择"按量计费"模式（有免费额度）
- 输入环境名称（如：novel-app-prod）
- 选择地域（建议选择离用户最近的地域）

### 3. 获取环境信息

#### 获取环境ID
- 进入您创建的环境
- 在环境首页的"业务指标"区域上方可以看到**环境ID**
- 点击环境ID旁边的复制图标即可复制

#### 获取Client ID和Client Secret
- 在环境控制台左侧菜单中找到"登录授权"
- 在登录授权列表中找到"自定义登录"
- 点击启用开关开启自定义登录
- 点击"编辑"按钮进行配置
- 在配置页面可以获取到Client ID和Client Secret

## 第二步：配置应用信息

### 1. 修改配置文件
打开 `lib/config/cloudbase_config.dart`，将以下信息替换为您的实际环境信息：

```dart
static const String envId = 'YOUR_ENV_ID'; // 替换为您的环境ID
static const String clientId = 'YOUR_CLIENT_ID'; // 替换为您的Client ID
static const String clientSecret = 'YOUR_CLIENT_SECRET'; // 替换为您的Client Secret
```

### 2. 配置登录授权
在腾讯云开发控制台中：

#### 步骤1：启用邮箱登录
1. 在左侧菜单中点击"登录授权"
2. 找到"邮箱登录"选项
3. 点击右侧的开关启用邮箱登录

#### 步骤2：启用用户名密码登录
1. 在登录授权列表中找到"用户名密码登录"
2. 点击右侧的开关启用

#### 步骤3：配置自定义登录（重要）
1. 找到"自定义登录"选项
2. 点击右侧的开关启用
3. 点击"编辑"按钮
4. 在弹出的配置页面中：
   - 记录下显示的**Client ID**
   - 记录下显示的**Client Secret**
   - 这两个值需要填入应用配置文件中

## 第三步：创建数据库集合

在腾讯云开发控制台的"数据库"页面创建以下集合：

### 1. userSettings（用户设置）
```json
{
  "_id": "自动生成",
  "userId": "用户ID",
  "settings": {
    "theme": "dark",
    "fontSize": 16,
    "autoSave": true
  },
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

### 2. userFavorites（用户收藏）
```json
{
  "_id": "自动生成",
  "userId": "用户ID",
  "favorites": [
    {
      "id": "novelId",
      "title": "小说标题",
      "author": "作者",
      "cover": "封面URL",
      "addedAt": "2024-01-01T00:00:00Z"
    }
  ],
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

### 3. userHistory（用户历史记录）
```json
{
  "_id": "自动生成",
  "userId": "用户ID",
  "history": [
    {
      "novelId": "小说ID",
      "chapterId": "章节ID",
      "title": "章节标题",
      "progress": 0.5,
      "timestamp": "2024-01-01T00:00:00Z"
    }
  ],
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

### 4. memberData（会员专属数据）
```json
{
  "_id": "自动生成",
  "userId": "用户ID",
  "memberData": {
    "advancedSettings": {},
    "customTemplates": [],
    "priorityQueue": true
  },
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

## 第四步：配置数据库权限

为每个集合设置以下权限规则：

### 用户级权限（推荐）
```javascript
// 仅允许用户访问自己的数据
{
  "read": "auth.uid == resource.userId",
  "write": "auth.uid == resource.userId"
}
```

### 开放权限（测试用）
```javascript
// 允许所有已登录用户访问（仅用于测试）
{
  "read": true,
  "write": true
}
```

## 第五步：测试配置

### 1. 启动应用
```bash
flutter run
```

### 2. 测试功能
1. **注册新用户**：使用邮箱和密码注册
2. **登录测试**：使用注册的账号登录
3. **数据同步测试**：
   - 升级为会员（测试功能）
   - 在会员中心执行数据同步
   - 检查腾讯云开发控制台中的数据库记录

## 功能说明

### 用户认证功能
- ✅ 邮箱注册/登录
- ✅ 用户名密码登录
- ✅ 密码重置（通过邮箱验证码）
- ✅ 自动登录保持
- ✅ 用户信息管理

### 会员系统
- ✅ 会员状态管理
- ✅ 会员权限控制
- ✅ 会员到期检查
- ✅ 升级会员功能

### 数据同步功能（仅会员）
- ✅ 用户设置云同步
- ✅ 收藏数据同步
- ✅ 历史记录同步
- ✅ 会员专属数据存储
- ✅ 冲突解决机制
- ✅ 离线数据缓存

## 使用说明

### 1. 用户认证
```dart
final authController = Get.find<AuthController>();

// 注册
await authController.register('username', '<EMAIL>', 'password');

// 登录
await authController.login('<EMAIL>', 'password');

// 登出
await authController.logout();
```

### 2. 数据同步
```dart
final syncService = Get.find<CloudBaseSyncService>();

// 执行完整同步
await syncService.performFullSync();

// 强制上传本地数据
await syncService.forceUpload();

// 清除云端数据
await syncService.clearCloudData();
```

### 3. 会员功能
```dart
final authController = Get.find<AuthController>();

// 检查是否为会员
bool isVip = authController.isVipUser;

// 获取会员剩余天数
int remainingDays = authController.vipRemainingDays;

// 升级为会员
await authController.upgradeToVip();
```

## 界面导航

- **会员中心**: `/cloudbase-member` - 腾讯云开发会员功能管理界面
- **用户资料**: `/profile` - 用户信息管理
- **登录界面**: `/login` - 用户登录
- **注册界面**: `/register` - 用户注册

## 费用说明

腾讯云开发免费额度：
- 数据库读写：每月5万次
- 数据库存储：2GB
- 云函数调用：每月10万次
- 网络流量：每月1GB

超出免费额度后按量付费，具体价格请参考腾讯云开发官网。

## 故障排除

### 1. 初始化失败
- 检查网络连接
- 确认环境ID、Client ID和Client Secret正确
- 查看控制台错误信息

### 2. 登录失败
- 确认邮箱/密码正确
- 检查登录授权是否已启用
- 查看腾讯云开发控制台用户数据

### 3. 数据同步失败
- 确认用户为会员状态
- 检查数据库权限配置
- 查看同步状态信息

### 4. 权限问题
- 确认数据库集合权限设置
- 检查用户认证状态
- 验证数据访问权限

## 安全建议

1. **生产环境配置**
   - 使用环境变量存储敏感信息
   - 启用HTTPS通信
   - 配置适当的数据库权限

2. **数据保护**
   - 定期备份重要数据
   - 设置数据访问权限
   - 监控异常访问行为

3. **用户隐私**
   - 遵守数据保护法规
   - 提供数据删除选项
   - 明确隐私政策

## 技术支持

如果遇到问题，可以：
1. 查看腾讯云开发官方文档
2. 访问腾讯云开发社区论坛
3. 联系腾讯云技术支持
4. 查看本项目的GitHub Issues

---

配置完成后，您的小说应用将具备完整的用户认证和数据同步功能！
