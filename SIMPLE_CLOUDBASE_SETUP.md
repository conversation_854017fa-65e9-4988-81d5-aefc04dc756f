# 超简单腾讯云开发配置指南

## 只需3步，无需复杂配置！

### 第1步：创建环境并获取环境ID

1. **打开腾讯云开发控制台**
   - 访问：https://console.cloud.tencent.com/tcb
   - 登录您的腾讯云账号

2. **创建环境**
   - 点击"新建环境"
   - 填写环境名称（如：novel-app）
   - 选择"按量计费"
   - 点击"立即开通"

3. **复制环境ID**
   - 环境创建完成后，在首页"业务指标"上方可以看到环境ID
   - 格式类似：`env-xxxxxxxx`
   - 点击复制图标复制环境ID

### 第2步：启用邮箱登录

1. **进入登录授权**
   - 在左侧菜单点击"登录授权"

2. **启用邮箱登录**
   - 找到"邮箱登录"选项
   - 点击右侧开关启用

**就这么简单！不需要配置其他复杂的登录方式。**

### 第3步：配置应用

1. **打开配置文件**
   - 打开 `lib/config/cloudbase_config.dart`

2. **填入环境ID**
   ```dart
   static const String envId = 'env-xxxxxxxx'; // 替换为您的环境ID
   ```

3. **创建数据库集合**
   - 在控制台点击"数据库"
   - 创建4个集合：
     - `userSettings`
     - `userFavorites`
     - `userHistory`
     - `memberData`
   - 每个集合的权限设置为：
     ```javascript
     {
       "read": true,
       "write": true
     }
     ```

## 完成！

现在运行 `flutter run` 就可以测试了：

1. **注册测试**：使用邮箱和密码注册
2. **登录测试**：使用注册的邮箱登录
3. **会员功能**：访问 `/cloudbase-member` 页面测试

## 为什么这么简单？

- ✅ **无需Client Secret**：直接使用邮箱登录，更安全更简单
- ✅ **无需验证码**：腾讯云开发自动处理邮箱验证
- ✅ **无需复杂配置**：只需环境ID即可
- ✅ **开箱即用**：代码已经适配邮箱登录模式

## 常见问题

**Q: 找不到环境ID？**
A: 在环境首页的"业务指标"区域上方，格式是 `env-xxxxxxxx`

**Q: 邮箱登录不工作？**
A: 确保在"登录授权"页面启用了"邮箱登录"

**Q: 数据库错误？**
A: 确保创建了4个必需的集合并设置了正确的权限

**Q: 注册失败？**
A: 检查邮箱格式是否正确，密码是否符合要求

---

🎉 就是这么简单！享受您的腾讯云开发之旅吧！
