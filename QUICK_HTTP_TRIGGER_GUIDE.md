# 快速配置HTTP触发器

## 问题
应用显示404错误，说明云函数的HTTP触发器没有配置。

## 解决方案（5分钟搞定）

### 第1步：配置HTTP触发器

对于每个已创建的云函数，执行以下操作：

#### 1. auth-signup 函数
1. 点击云函数 `auth-signup`
2. 点击"触发器"选项卡
3. 点击"新建触发器"
4. 选择"HTTP触发器"
5. 路径填入：`/auth-signup`
6. 方法选择：`POST`
7. 启用鉴权：**关闭**
8. 点击确定

#### 2. auth-signin 函数
- 路径：`/auth-signin`
- 方法：`POST`
- 启用鉴权：关闭

#### 3. auth-userinfo 函数
- 路径：`/auth-userinfo`
- 方法：`POST`
- 启用鉴权：关闭

#### 4. auth-reset-password 函数
- 路径：`/auth-reset-password`
- 方法：`POST`
- 启用鉴权：关闭

### 第2步：验证配置

配置完成后，在触发器列表中应该能看到类似这样的URL：
```
https://novel-app-2gywkgnn15cbd6a8.service.tcloudbase.com/auth-signup
https://novel-app-2gywkgnn15cbd6a8.service.tcloudbase.com/auth-signin
https://novel-app-2gywkgnn15cbd6a8.service.tcloudbase.com/auth-userinfo
https://novel-app-2gywkgnn15cbd6a8.service.tcloudbase.com/auth-reset-password
```

### 第3步：测试

在云函数页面点击"测试"，输入：
```json
{
  "email": "<EMAIL>",
  "password": "123456",
  "username": "testuser"
}
```

如果返回类似这样的结果，说明配置成功：
```json
{
  "code": 0,
  "message": "Registration successful",
  "data": {
    "uid": "user_1234567890_abc123",
    "email": "<EMAIL>",
    "username": "testuser",
    "isVip": false,
    "isAdmin": false
  }
}
```

### 第4步：运行应用

现在运行 `flutter run`，应用的注册登录功能就可以正常工作了！

## 重要提示

- ⚠️ **路径必须以 `/` 开头**
- ⚠️ **方法必须选择 `POST`**
- ⚠️ **暂时关闭鉴权**（测试阶段）
- ⚠️ **等待1-2分钟让配置生效**

## 如果还是404

1. **检查云函数是否部署成功**
2. **确认触发器路径正确**
3. **等待几分钟再测试**
4. **重新部署云函数**

配置完成后，您的应用就可以正常使用了！
