# 字段命名修改说明

## 修改原因

腾讯云开发的数据库字段名不能以下划线开头，因此我们将所有字段名从 `snake_case` 改为 `camelCase` 格式。

## 数据库集合名称修改

| 原名称 | 新名称 | 说明 |
|--------|--------|------|
| `user_settings` | `userSettings` | 用户设置集合 |
| `user_favorites` | `userFavorites` | 用户收藏集合 |
| `user_history` | `userHistory` | 用户历史记录集合 |
| `member_data` | `memberData` | 会员专属数据集合 |

## 数据库字段名修改

### 通用字段
| 原字段名 | 新字段名 | 说明 |
|----------|----------|------|
| `user_id` | `userId` | 用户ID |
| `created_at` | `createdAt` | 创建时间 |
| `updated_at` | `updatedAt` | 更新时间 |

### 用户设置相关
| 原字段名 | 新字段名 | 说明 |
|----------|----------|------|
| `font_size` | `fontSize` | 字体大小 |
| `auto_save` | `autoSave` | 自动保存 |

### 收藏相关
| 原字段名 | 新字段名 | 说明 |
|----------|----------|------|
| `novel_id` | `novelId` | 小说ID |
| `added_at` | `addedAt` | 添加时间 |

### 历史记录相关
| 原字段名 | 新字段名 | 说明 |
|----------|----------|------|
| `novel_id` | `novelId` | 小说ID |
| `chapter_id` | `chapterId` | 章节ID |

### 会员数据相关
| 原字段名 | 新字段名 | 说明 |
|----------|----------|------|
| `member_data` | `memberData` | 会员数据 |
| `advanced_settings` | `advancedSettings` | 高级设置 |
| `custom_templates` | `customTemplates` | 自定义模板 |
| `priority_queue` | `priorityQueue` | 优先队列 |

### 用户信息相关
| 原字段名 | 新字段名 | 说明 |
|----------|----------|------|
| `is_vip` | `isVip` | 是否为会员 |
| `is_admin` | `isAdmin` | 是否为管理员 |
| `vip_expire_time` | `vipExpireTime` | 会员到期时间 |

## 本地存储键名修改

| 原键名 | 新键名 | 说明 |
|--------|--------|------|
| `auth_token` | `authToken` | 认证令牌 |
| `user_data` | `userData` | 用户数据 |
| `use_cloudbase` | `useCloudbase` | 使用腾讯云开发 |
| `cloudbase_user_info` | `cloudbaseUserInfo` | 腾讯云开发用户信息 |
| `cloudbase_last_sync_time` | `cloudbaseLastSyncTime` | 最后同步时间 |
| `cloudbase_sync_enabled` | `cloudbaseSyncEnabled` | 同步启用状态 |
| `cloudbase_user_id` | `cloudbaseUserId` | 腾讯云开发用户ID |
| `settings_update_time` | `settingsUpdateTime` | 设置更新时间 |
| `user_favorites` | `userFavorites` | 用户收藏 |
| `user_history` | `userHistory` | 用户历史记录 |
| `member_data` | `memberData` | 会员数据 |

## 代码中的前缀修改

### 设置相关前缀
| 原前缀 | 新前缀 | 说明 |
|--------|--------|------|
| `setting_` | `setting` | 设置前缀 |
| `config_` | `config` | 配置前缀 |
| `user_preference_` | `userPreference` | 用户偏好前缀 |

## 权限规则修改

### 原权限规则
```javascript
{
  "read": "auth.uid == resource.user_id",
  "write": "auth.uid == resource.user_id"
}
```

### 新权限规则
```javascript
{
  "read": "auth.uid == resource.userId",
  "write": "auth.uid == resource.userId"
}
```

## 数据结构示例

### userSettings 集合
```json
{
  "_id": "自动生成",
  "userId": "用户ID",
  "settings": {
    "theme": "dark",
    "fontSize": 16,
    "autoSave": true
  },
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

### userFavorites 集合
```json
{
  "_id": "自动生成",
  "userId": "用户ID",
  "favorites": [
    {
      "id": "novelId",
      "title": "小说标题",
      "author": "作者",
      "cover": "封面URL",
      "addedAt": "2024-01-01T00:00:00Z"
    }
  ],
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

### userHistory 集合
```json
{
  "_id": "自动生成",
  "userId": "用户ID",
  "history": [
    {
      "novelId": "小说ID",
      "chapterId": "章节ID",
      "title": "章节标题",
      "progress": 0.5,
      "timestamp": "2024-01-01T00:00:00Z"
    }
  ],
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

### memberData 集合
```json
{
  "_id": "自动生成",
  "userId": "用户ID",
  "memberData": {
    "advancedSettings": {},
    "customTemplates": [],
    "priorityQueue": true
  },
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

## 注意事项

1. **向后兼容性**：如果您已经有旧数据，需要进行数据迁移
2. **权限设置**：记得更新数据库权限规则中的字段名
3. **API调用**：确保所有API调用使用新的字段名
4. **测试验证**：建议在测试环境先验证所有功能正常

## 迁移建议

如果您已经创建了使用旧字段名的数据：

1. **备份数据**：先备份现有数据
2. **创建新集合**：使用新的集合名称
3. **数据迁移**：编写脚本将旧数据转换为新格式
4. **测试验证**：确保所有功能正常工作
5. **删除旧集合**：确认无误后删除旧集合

---

所有修改已完成，现在您可以按照新的字段名创建数据库集合了！
