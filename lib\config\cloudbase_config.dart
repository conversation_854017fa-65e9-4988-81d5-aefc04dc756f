import 'dart:convert';

/// 腾讯云开发配置文件
/// 请在腾讯云开发控制台获取您的应用信息并填入以下配置
class CloudBaseConfig {
  // 腾讯云开发应用配置
  // 请替换为您的实际应用信息
  static const String envId = 'novel-app-2gywkgnn15cbd6a8'; // 请替换为您的环境ID

  // 注意：使用邮箱登录模式，不需要Client ID和Client Secret
  
  // API基础URL配置 - 使用腾讯云开发HTTP访问服务
  static String get baseUrl => 'https://$envId.service.tcloudbase.com';

  // 云函数HTTP触发器端点
  static const String signUpEndpoint = '/auth-signup';
  static const String signInEndpoint = '/auth-signin';
  static const String signOutEndpoint = '/auth-signout';
  static const String userInfoEndpoint = '/auth-userinfo';
  static const String resetPasswordEndpoint = '/auth-reset-password';
  
  // 数据库相关端点
  static const String queryEndpoint = '/database/query';
  static const String insertEndpoint = '/database/add';
  static const String updateEndpoint = '/database/update';
  static const String deleteEndpoint = '/database/remove';
  
  // 数据表名称配置
  static const String userSettingsCollection = 'userSettings'; // 用户设置表
  static const String userFavoritesCollection = 'userFavorites'; // 用户收藏表
  static const String userHistoryCollection = 'userHistory'; // 用户历史记录表
  static const String memberDataCollection = 'memberData'; // 会员专属数据表
  
  // 会员功能配置
  static const int freeUserMaxNovels = 5; // 免费用户最大小说数量
  static const int memberMaxNovels = 100; // 会员用户最大小说数量
  static const int freeUserMaxChapters = 10; // 免费用户每本小说最大章节数
  static const int memberMaxChapters = 1000; // 会员用户每本小说最大章节数
  
  // 数据同步配置
  static const Duration syncInterval = Duration(minutes: 5); // 数据同步间隔
  static const int maxRetryCount = 3; // 最大重试次数
  static const Duration retryDelay = Duration(seconds: 2); // 重试延迟
  
  // 请求配置
  static const Duration requestTimeout = Duration(seconds: 30); // 请求超时时间
  static const int maxConcurrentRequests = 5; // 最大并发请求数
  
  // 验证码配置
  static const Duration verificationCodeExpiry = Duration(minutes: 5); // 验证码有效期
  static const int maxVerificationAttempts = 3; // 最大验证尝试次数
  
  // 会员相关配置
  static const Duration membershipDuration = Duration(days: 365); // 会员有效期
  static const List<String> memberFeatures = [
    'unlimited_novels',
    'unlimited_chapters', 
    'data_sync',
    'priority_generation',
    'advanced_settings',
  ]; // 会员功能列表
  
  // 错误重试配置
  static const List<int> retryableStatusCodes = [500, 502, 503, 504]; // 可重试的HTTP状态码
  static const Duration backoffDelay = Duration(milliseconds: 500); // 退避延迟
  
  /// 获取完整的API URL
  static String getApiUrl(String endpoint) {
    return '$baseUrl$endpoint';
  }
  
  /// 获取认证头
  static Map<String, String> getAuthHeaders({
    String? deviceId,
    String? requestId,
    String? accessToken,
  }) {
    final headers = <String, String>{
      'Content-Type': 'application/json',
      'User-Agent': 'Novel-App/1.0.0 (Flutter)',
    };
    
    if (deviceId != null) {
      headers['x-device-id'] = deviceId;
    }
    
    if (requestId != null) {
      headers['x-request-id'] = requestId;
    }
    
    if (accessToken != null) {
      headers['Authorization'] = 'Bearer $accessToken';
    }
    
    return headers;
  }
  
  /// 获取基础请求头（邮箱登录模式）
  static Map<String, String> getBasicHeaders() {
    return {
      'Content-Type': 'application/json',
      'User-Agent': 'Novel-App/1.0.0 (Flutter)',
    };
  }
}
