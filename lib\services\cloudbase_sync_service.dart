import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:novel_app/config/cloudbase_config.dart';
import 'package:novel_app/services/cloudbase_service.dart';
import 'package:novel_app/services/cloudbase_database_service.dart';

/// 腾讯云开发用户数据同步服务
/// 负责会员用户的数据云同步功能
class CloudBaseSyncService extends GetxService {
  final _storage = GetStorage();
  final _cloudBase = CloudBaseService.instance;
  final _database = CloudBaseDatabaseService.instance;
  
  // 同步状态
  final RxBool isSyncing = false.obs;
  final RxString syncStatus = '未同步'.obs;
  final Rx<DateTime> lastSyncTime = DateTime.now().obs;
  
  // 存储键
  static const String _lastSyncKey = 'cloudbaseLastSyncTime';
  static const String _syncEnabledKey = 'cloudbaseSyncEnabled';
  static const String _userIdKey = 'cloudbaseUserId';
  
  @override
  void onInit() {
    super.onInit();
    _loadSyncSettings();
  }
  
  /// 加载同步设置
  void _loadSyncSettings() {
    final lastSync = _storage.read(_lastSyncKey);
    if (lastSync != null) {
      lastSyncTime.value = DateTime.parse(lastSync);
    }
  }
  
  /// 检查用户是否为会员且可以同步
  bool get canSync {
    if (!_cloudBase.isLoggedIn) return false;
    
    // 检查用户是否为会员（这里需要根据您的会员逻辑调整）
    final userInfo = _storage.read('cloudbaseUserInfo');
    if (userInfo == null) return false;

    final isVip = userInfo['isVip'] as bool? ?? false;
    return isVip;
  }
  
  /// 获取当前用户ID
  String? get _currentUserId {
    final userInfo = _storage.read('cloudbaseUserInfo');
    return userInfo?['sub'] ?? userInfo?['userId'];
  }
  
  /// 启用/禁用数据同步
  void setSyncEnabled(bool enabled) {
    _storage.write(_syncEnabledKey, enabled);
  }
  
  /// 检查是否启用同步
  bool get isSyncEnabled {
    return _storage.read(_syncEnabledKey) ?? true;
  }
  
  /// 执行完整数据同步
  Future<bool> performFullSync() async {
    if (!canSync || !isSyncEnabled) {
      syncStatus.value = '同步未启用或非会员用户';
      return false;
    }
    
    final userId = _currentUserId;
    if (userId == null) {
      syncStatus.value = '用户ID获取失败';
      return false;
    }
    
    isSyncing.value = true;
    syncStatus.value = '正在同步...';
    
    try {
      // 同步用户设置
      await _syncUserSettings(userId);
      
      // 同步用户收藏
      await _syncUserFavorites(userId);
      
      // 同步用户历史记录
      await _syncUserHistory(userId);
      
      // 同步会员专属数据
      await _syncMemberData(userId);
      
      // 更新同步时间
      lastSyncTime.value = DateTime.now();
      _storage.write(_lastSyncKey, lastSyncTime.value.toIso8601String());
      
      syncStatus.value = '同步完成';
      debugPrint('数据同步完成');
      return true;
    } catch (e) {
      syncStatus.value = '同步失败: $e';
      debugPrint('数据同步失败: $e');
      return false;
    } finally {
      isSyncing.value = false;
    }
  }
  
  /// 同步用户设置
  Future<void> _syncUserSettings(String userId) async {
    try {
      // 获取本地设置
      final localSettings = _getAllLocalSettings();
      
      // 获取云端设置
      final cloudSettings = await _database.getUserSettings(userId);
      
      if (cloudSettings == null) {
        // 云端没有设置，上传本地设置
        await _database.saveUserSettings(
          userId: userId,
          settings: localSettings,
        );
        debugPrint('用户设置已上传到云端');
      } else {
        // 比较时间戳，同步最新的设置
        final localUpdateTime = _storage.read('settingsUpdateTime') != null
            ? DateTime.parse(_storage.read('settingsUpdateTime'))
            : DateTime.now().subtract(const Duration(days: 1));
        
        // 这里简化处理，直接使用云端设置
        // 实际应用中可以根据时间戳决定同步方向
        _applyCloudSettings(cloudSettings);
        debugPrint('用户设置已从云端同步');
      }
    } catch (e) {
      debugPrint('同步用户设置失败: $e');
    }
  }
  
  /// 获取所有本地设置
  Map<String, dynamic> _getAllLocalSettings() {
    final keys = _storage.getKeys();
    final settings = <String, dynamic>{};
    
    for (final key in keys) {
      if (key.startsWith('setting') ||
          key.startsWith('config') ||
          key.startsWith('userPreference')) {
        settings[key] = _storage.read(key);
      }
    }
    
    return settings;
  }
  
  /// 应用云端设置到本地
  void _applyCloudSettings(Map<String, dynamic> cloudSettings) {
    for (final entry in cloudSettings.entries) {
      _storage.write(entry.key, entry.value);
    }
    
    _storage.write('settingsUpdateTime', DateTime.now().toIso8601String());
  }
  
  /// 同步用户收藏
  Future<void> _syncUserFavorites(String userId) async {
    try {
      // 获取本地收藏
      final localFavorites = _getLocalFavorites();
      
      // 获取云端收藏
      final cloudFavorites = await _database.getUserFavorites(userId);
      
      if (cloudFavorites == null || cloudFavorites.isEmpty) {
        // 云端没有收藏，上传本地收藏
        if (localFavorites.isNotEmpty) {
          await _database.saveUserFavorites(
            userId: userId,
            favorites: localFavorites,
          );
          debugPrint('用户收藏已上传到云端');
        }
      } else {
        // 合并本地和云端收藏（去重）
        final mergedFavorites = _mergeFavorites(localFavorites, cloudFavorites);
        
        // 保存合并后的收藏到云端
        await _database.saveUserFavorites(
          userId: userId,
          favorites: mergedFavorites,
        );
        
        // 更新本地收藏
        _saveLocalFavorites(mergedFavorites);
        debugPrint('用户收藏已同步');
      }
    } catch (e) {
      debugPrint('同步用户收藏失败: $e');
    }
  }
  
  /// 获取本地收藏
  List<Map<String, dynamic>> _getLocalFavorites() {
    final favorites = _storage.read('userFavorites');
    if (favorites is List) {
      return List<Map<String, dynamic>>.from(favorites);
    }
    return [];
  }

  /// 保存本地收藏
  void _saveLocalFavorites(List<Map<String, dynamic>> favorites) {
    _storage.write('userFavorites', favorites);
  }
  
  /// 合并收藏列表
  List<Map<String, dynamic>> _mergeFavorites(
    List<Map<String, dynamic>> local,
    List<Map<String, dynamic>> cloud,
  ) {
    final merged = <Map<String, dynamic>>[];
    final seenIds = <String>{};
    
    // 添加云端收藏
    for (final item in cloud) {
      final id = item['id']?.toString();
      if (id != null && !seenIds.contains(id)) {
        merged.add(item);
        seenIds.add(id);
      }
    }
    
    // 添加本地收藏（去重）
    for (final item in local) {
      final id = item['id']?.toString();
      if (id != null && !seenIds.contains(id)) {
        merged.add(item);
        seenIds.add(id);
      }
    }
    
    return merged;
  }
  
  /// 同步用户历史记录
  Future<void> _syncUserHistory(String userId) async {
    try {
      // 获取本地历史记录
      final localHistory = _getLocalHistory();
      
      // 获取云端历史记录
      final cloudHistory = await _database.getUserHistory(userId);
      
      if (cloudHistory == null || cloudHistory.isEmpty) {
        // 云端没有历史记录，上传本地历史记录
        if (localHistory.isNotEmpty) {
          await _database.saveUserHistory(
            userId: userId,
            history: localHistory,
          );
          debugPrint('用户历史记录已上传到云端');
        }
      } else {
        // 合并本地和云端历史记录
        final mergedHistory = _mergeHistory(localHistory, cloudHistory);
        
        // 保存合并后的历史记录到云端
        await _database.saveUserHistory(
          userId: userId,
          history: mergedHistory,
        );
        
        // 更新本地历史记录
        _saveLocalHistory(mergedHistory);
        debugPrint('用户历史记录已同步');
      }
    } catch (e) {
      debugPrint('同步用户历史记录失败: $e');
    }
  }
  
  /// 获取本地历史记录
  List<Map<String, dynamic>> _getLocalHistory() {
    final history = _storage.read('userHistory');
    if (history is List) {
      return List<Map<String, dynamic>>.from(history);
    }
    return [];
  }

  /// 保存本地历史记录
  void _saveLocalHistory(List<Map<String, dynamic>> history) {
    _storage.write('userHistory', history);
  }
  
  /// 合并历史记录列表
  List<Map<String, dynamic>> _mergeHistory(
    List<Map<String, dynamic>> local,
    List<Map<String, dynamic>> cloud,
  ) {
    final merged = <Map<String, dynamic>>[];
    final seenKeys = <String>{};
    
    // 添加云端历史记录
    for (final item in cloud) {
      final key = '${item['novelId']}_${item['chapterId']}';
      if (!seenKeys.contains(key)) {
        merged.add(item);
        seenKeys.add(key);
      }
    }

    // 添加本地历史记录（去重）
    for (final item in local) {
      final key = '${item['novelId']}_${item['chapterId']}';
      if (!seenKeys.contains(key)) {
        merged.add(item);
        seenKeys.add(key);
      }
    }
    
    // 按时间排序（最新的在前）
    merged.sort((a, b) {
      final timeA = DateTime.tryParse(a['timestamp'] ?? '') ?? DateTime(1970);
      final timeB = DateTime.tryParse(b['timestamp'] ?? '') ?? DateTime(1970);
      return timeB.compareTo(timeA);
    });
    
    return merged;
  }
  
  /// 同步会员专属数据
  Future<void> _syncMemberData(String userId) async {
    try {
      // 获取本地会员数据
      final localMemberData = _getLocalMemberData();
      
      // 获取云端会员数据
      final cloudMemberData = await _database.getMemberData(userId);
      
      if (cloudMemberData == null) {
        // 云端没有会员数据，上传本地数据
        if (localMemberData.isNotEmpty) {
          await _database.saveMemberData(
            userId: userId,
            memberData: localMemberData,
          );
          debugPrint('会员专属数据已上传到云端');
        }
      } else {
        // 合并本地和云端会员数据
        final mergedData = {...localMemberData, ...cloudMemberData};
        
        // 保存合并后的数据到云端
        await _database.saveMemberData(
          userId: userId,
          memberData: mergedData,
        );
        
        // 更新本地会员数据
        _saveLocalMemberData(mergedData);
        debugPrint('会员专属数据已同步');
      }
    } catch (e) {
      debugPrint('同步会员专属数据失败: $e');
    }
  }
  
  /// 获取本地会员数据
  Map<String, dynamic> _getLocalMemberData() {
    final memberData = _storage.read('memberData');
    if (memberData is Map) {
      return Map<String, dynamic>.from(memberData);
    }
    return {};
  }

  /// 保存本地会员数据
  void _saveLocalMemberData(Map<String, dynamic> memberData) {
    _storage.write('memberData', memberData);
  }
  
  /// 强制上传本地数据
  Future<bool> forceUpload() async {
    if (!canSync) return false;
    
    final userId = _currentUserId;
    if (userId == null) return false;
    
    try {
      // 上传所有本地数据
      await _database.saveUserSettings(
        userId: userId,
        settings: _getAllLocalSettings(),
      );
      
      await _database.saveUserFavorites(
        userId: userId,
        favorites: _getLocalFavorites(),
      );
      
      await _database.saveUserHistory(
        userId: userId,
        history: _getLocalHistory(),
      );
      
      await _database.saveMemberData(
        userId: userId,
        memberData: _getLocalMemberData(),
      );
      
      syncStatus.value = '强制上传完成';
      return true;
    } catch (e) {
      debugPrint('强制上传失败: $e');
      return false;
    }
  }
  
  /// 清除云端数据
  Future<bool> clearCloudData() async {
    if (!canSync) return false;
    
    final userId = _currentUserId;
    if (userId == null) return false;
    
    try {
      await _database.clearUserData(userId);
      syncStatus.value = '云端数据已清除';
      return true;
    } catch (e) {
      debugPrint('清除云端数据失败: $e');
      return false;
    }
  }
}
