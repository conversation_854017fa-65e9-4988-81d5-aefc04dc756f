import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/controllers/auth_controller.dart';
import 'package:novel_app/services/cloudbase_sync_service.dart';

/// 腾讯云开发会员功能管理界面
class CloudBaseMemberScreen extends StatelessWidget {
  final authController = Get.find<AuthController>();
  final syncService = Get.find<CloudBaseSyncService>();

  CloudBaseMemberScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('会员中心'),
        elevation: 0,
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Obx(() {
        if (!authController.isLoggedIn.value) {
          return const Center(
            child: Text('请先登录'),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildUserInfoCard(),
              const SizedBox(height: 20),
              _buildMemberStatusCard(),
              const SizedBox(height: 20),
              _buildCloudBaseStatusCard(),
              const SizedBox(height: 20),
              _buildSyncStatusCard(),
              const SizedBox(height: 20),
              _buildMemberFeatures(),
              const SizedBox(height: 20),
              _buildSyncControls(),
              const SizedBox(height: 20),
              _buildSystemControls(),
            ],
          ),
        );
      }),
    );
  }

  /// 用户信息卡片
  Widget _buildUserInfoCard() {
    final user = authController.currentUser.value;
    if (user == null) return const SizedBox();

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '用户信息',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(Icons.person, color: Colors.blue),
                const SizedBox(width: 8),
                Text('用户名: ${user.username}'),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.email, color: Colors.green),
                const SizedBox(width: 8),
                Text('邮箱: ${user.email}'),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  user.isVip ? Icons.star : Icons.star_border,
                  color: user.isVip ? Colors.amber : Colors.grey,
                ),
                const SizedBox(width: 8),
                Text(user.isVip ? '会员用户' : '普通用户'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 会员状态卡片
  Widget _buildMemberStatusCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '会员状态',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            if (authController.isVipUser) ...[
              Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.green),
                  const SizedBox(width: 8),
                  const Text('会员有效'),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.schedule, color: Colors.orange),
                  const SizedBox(width: 8),
                  Text('剩余天数: ${authController.vipRemainingDays}天'),
                ],
              ),
            ] else ...[
              Row(
                children: [
                  const Icon(Icons.cancel, color: Colors.red),
                  const SizedBox(width: 8),
                  const Text('非会员用户'),
                ],
              ),
              const SizedBox(height: 12),
              ElevatedButton(
                onPressed: () => authController.upgradeToVip(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.amber,
                  foregroundColor: Colors.white,
                ),
                child: const Text('升级为会员'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 腾讯云开发状态卡片
  Widget _buildCloudBaseStatusCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '腾讯云开发状态',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Obx(() => Row(
              children: [
                Icon(
                  authController.useCloudBase.value ? Icons.cloud : Icons.cloud_off,
                  color: authController.useCloudBase.value ? Colors.green : Colors.grey,
                ),
                const SizedBox(width: 8),
                Text(authController.useCloudBase.value ? '腾讯云开发模式' : '传统API模式'),
              ],
            )),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.link, color: Colors.blue),
                const SizedBox(width: 8),
                Text('连接状态: ${authController.isLoggedIn.value ? "已连接" : "未连接"}'),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => authController.switchAuthMode(true),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: authController.useCloudBase.value ? Colors.green : Colors.grey,
                    ),
                    child: const Text('腾讯云开发'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => authController.switchAuthMode(false),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: !authController.useCloudBase.value ? Colors.green : Colors.grey,
                    ),
                    child: const Text('传统API'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 数据同步状态卡片
  Widget _buildSyncStatusCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '数据同步',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Obx(() => Row(
              children: [
                Icon(
                  syncService.isSyncing.value ? Icons.sync : Icons.sync_disabled,
                  color: syncService.isSyncing.value ? Colors.blue : Colors.grey,
                ),
                const SizedBox(width: 8),
                Expanded(child: Text('状态: ${syncService.syncStatus.value}')),
              ],
            )),
            const SizedBox(height: 8),
            Obx(() => Row(
              children: [
                const Icon(Icons.access_time, color: Colors.grey),
                const SizedBox(width: 8),
                Text('上次同步: ${_formatDateTime(syncService.lastSyncTime.value)}'),
              ],
            )),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  syncService.canSync ? Icons.cloud : Icons.cloud_off,
                  color: syncService.canSync ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(syncService.canSync ? '可以同步' : '无法同步（需要会员）'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 会员功能列表
  Widget _buildMemberFeatures() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '会员特权',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildFeatureItem(
              icon: Icons.cloud_sync,
              title: '数据云同步',
              description: '设置、收藏、历史记录云端同步',
              isAvailable: authController.isVipUser,
            ),
            _buildFeatureItem(
              icon: Icons.library_books,
              title: '无限小说数量',
              description: '创建无限数量的小说作品',
              isAvailable: authController.isVipUser,
            ),
            _buildFeatureItem(
              icon: Icons.auto_stories,
              title: '无限章节数量',
              description: '每本小说可创建无限章节',
              isAvailable: authController.isVipUser,
            ),
            _buildFeatureItem(
              icon: Icons.priority_high,
              title: '优先生成',
              description: '享受AI生成优先级',
              isAvailable: authController.isVipUser,
            ),
          ],
        ),
      ),
    );
  }

  /// 功能项目
  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
    required bool isAvailable,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(
            icon,
            color: isAvailable ? Colors.green : Colors.grey,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: isAvailable ? Colors.black : Colors.grey,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: isAvailable ? Colors.grey[600] : Colors.grey,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            isAvailable ? Icons.check_circle : Icons.lock,
            color: isAvailable ? Colors.green : Colors.grey,
          ),
        ],
      ),
    );
  }

  /// 同步控制按钮
  Widget _buildSyncControls() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '同步控制',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: syncService.canSync && !syncService.isSyncing.value
                        ? () => syncService.performFullSync()
                        : null,
                    icon: const Icon(Icons.sync),
                    label: const Text('立即同步'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: syncService.canSync
                        ? () => syncService.forceUpload()
                        : null,
                    icon: const Icon(Icons.cloud_upload),
                    label: const Text('强制上传'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: syncService.canSync
                    ? () => _showClearDataDialog()
                    : null,
                icon: const Icon(Icons.delete_forever, color: Colors.red),
                label: const Text('清除云端数据', style: TextStyle(color: Colors.red)),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 系统控制按钮
  Widget _buildSystemControls() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '系统控制',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => authController.logout(),
                icon: const Icon(Icons.logout),
                label: const Text('退出登录'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示清除数据确认对话框
  void _showClearDataDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('确认清除'),
        content: const Text('确定要清除所有云端数据吗？此操作不可恢复。'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              syncService.clearCloudData();
            },
            child: const Text('确认', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
