# 快速创建云函数指南

## 第一步：创建用户数据集合

在腾讯云开发控制台的数据库页面：
1. 点击"新建集合"
2. 集合名称：`users`
3. 权限设置：选择"读取全部数据，修改本人数据"
4. 点击确定

## 第二步：创建云函数

### 1. 创建注册函数

1. **进入云函数页面**，点击"新建云函数"
2. **函数名称**：`auth-signup`
3. **运行环境**：`Node.js 16`
4. **创建方式**：空白函数
5. **复制以下代码**：

```javascript
const tcb = require('@cloudbase/node-sdk');
const app = tcb.init({ env: tcb.SYMBOL_CURRENT_ENV });
const db = app.database();

exports.main = async (event, context) => {
  try {
    const { email, password, username } = event;
    
    if (!email || !password) {
      return { code: 400, message: '邮箱和密码不能为空' };
    }
    
    // 检查用户是否已存在
    const existingUser = await db.collection('users').where({ email }).get();
    if (existingUser.data.length > 0) {
      return { code: 409, message: '用户已存在' };
    }
    
    // 创建用户
    const userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    await db.collection('users').add({
      uid: userId,
      email,
      username: username || email.split('@')[0],
      password, // 注意：生产环境应加密
      isVip: false,
      isAdmin: false,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    return {
      code: 0,
      message: '注册成功',
      data: { uid: userId, email, username: username || email.split('@')[0], isVip: false, isAdmin: false }
    };
  } catch (error) {
    return { code: 500, message: error.message || '注册失败' };
  }
};
```

6. 点击"保存并部署"

### 2. 创建登录函数

1. **新建云函数**：`auth-signin`
2. **复制以下代码**：

```javascript
const tcb = require('@cloudbase/node-sdk');
const app = tcb.init({ env: tcb.SYMBOL_CURRENT_ENV });
const db = app.database();

exports.main = async (event, context) => {
  try {
    const { email, password } = event;
    
    if (!email || !password) {
      return { code: 400, message: '邮箱和密码不能为空' };
    }
    
    // 验证用户
    const userQuery = await db.collection('users').where({ email, password }).get();
    if (userQuery.data.length === 0) {
      return { code: 401, message: '邮箱或密码错误' };
    }
    
    const user = userQuery.data[0];
    
    // 更新登录时间
    await db.collection('users').doc(user._id).update({
      lastLoginAt: new Date(),
      updatedAt: new Date()
    });
    
    return {
      code: 0,
      message: '登录成功',
      data: {
        refresh_token: 'token_' + user.uid + '_' + Date.now(),
        user: {
          uid: user.uid,
          email: user.email,
          username: user.username,
          isVip: user.isVip,
          isAdmin: user.isAdmin,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        }
      }
    };
  } catch (error) {
    return { code: 500, message: error.message || '登录失败' };
  }
};
```

3. 点击"保存并部署"

### 3. 创建用户信息函数

1. **新建云函数**：`auth-userinfo`
2. **复制以下代码**：

```javascript
const tcb = require('@cloudbase/node-sdk');
const app = tcb.init({ env: tcb.SYMBOL_CURRENT_ENV });
const db = app.database();

exports.main = async (event, context) => {
  try {
    const { token } = event;
    
    if (!token) {
      return { code: 401, message: '未提供访问令牌' };
    }
    
    // 从token提取用户ID
    const parts = token.split('_');
    if (parts.length < 2) {
      return { code: 401, message: '无效的访问令牌' };
    }
    
    const uid = parts[1];
    const userQuery = await db.collection('users').where({ uid }).get();
    
    if (userQuery.data.length === 0) {
      return { code: 404, message: '用户不存在' };
    }
    
    const user = userQuery.data[0];
    return {
      code: 0,
      message: '获取用户信息成功',
      data: {
        uid: user.uid,
        email: user.email,
        username: user.username,
        isVip: user.isVip,
        isAdmin: user.isAdmin,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    };
  } catch (error) {
    return { code: 500, message: error.message || '获取用户信息失败' };
  }
};
```

3. 点击"保存并部署"

### 4. 创建密码重置函数

1. **新建云函数**：`auth-reset-password`
2. **复制以下代码**：

```javascript
const tcb = require('@cloudbase/node-sdk');
const app = tcb.init({ env: tcb.SYMBOL_CURRENT_ENV });
const db = app.database();

exports.main = async (event, context) => {
  try {
    const { email } = event;
    
    if (!email) {
      return { code: 400, message: '邮箱不能为空' };
    }
    
    // 检查用户是否存在
    const userQuery = await db.collection('users').where({ email }).get();
    if (userQuery.data.length === 0) {
      return { code: 404, message: '用户不存在' };
    }
    
    return { code: 0, message: '密码重置邮件已发送（功能待完善）' };
  } catch (error) {
    return { code: 500, message: error.message || '密码重置失败' };
  }
};
```

3. 点击"保存并部署"

## 第三步：测试云函数

在每个云函数页面，点击"测试"按钮：

### 测试注册
```json
{
  "email": "<EMAIL>",
  "password": "123456",
  "username": "testuser"
}
```

### 测试登录
```json
{
  "email": "<EMAIL>",
  "password": "123456"
}
```

## 完成！

创建完这4个云函数后，您的应用就可以正常进行用户注册和登录了！

现在可以运行 `flutter run` 测试应用功能。
