# 腾讯云开发快速开始指南

## 第一步：创建腾讯云开发环境

### 1. 访问控制台
- 打开浏览器，访问：https://console.cloud.tencent.com/tcb
- 使用您的腾讯云账号登录（如果没有账号，需要先注册）

### 2. 创建环境
- 点击"新建环境"按钮
- 填写环境名称（例如：novel-app）
- 选择计费方式：建议选择"按量计费"（有免费额度）
- 选择地域：建议选择离您最近的地域
- 点击"立即开通"

### 3. 等待环境创建
- 环境创建需要几分钟时间
- 创建完成后会自动跳转到环境控制台

## 第二步：获取环境配置信息

### 1. 获取环境ID
- 在环境首页，找到"业务指标"区域
- 在业务指标的上方可以看到**环境ID**（格式类似：env-xxxxxxxx）
- 点击环境ID旁边的复制图标复制环境ID

### 2. 配置登录授权
- 在左侧菜单中点击"登录授权"
- 启用以下登录方式：
  - **邮箱登录**：点击右侧开关启用

**注意**：只需要启用邮箱登录即可，不需要配置其他复杂的登录方式！

## 第三步：配置应用

### 1. 打开配置文件
打开项目中的 `lib/config/cloudbase_config.dart` 文件

### 2. 填入配置信息
将获取到的环境ID填入配置文件：

```dart
class CloudBaseConfig {
  // 将这里的值替换为您获取到的实际环境ID
  static const String envId = 'env-xxxxxxxx'; // 您的环境ID

  // 其他配置保持不变，使用邮箱登录模式不需要Client ID和Client Secret
}
```

## 第四步：创建数据库集合

### 1. 进入数据库页面
- 在腾讯云开发控制台左侧菜单中点击"数据库"
- 如果是第一次使用，需要先开通数据库服务

### 2. 创建集合
依次创建以下4个集合（点击"新建集合"）：

1. **userSettings**（用户设置）
2. **userFavorites**（用户收藏）
3. **userHistory**（用户历史记录）
4. **memberData**（会员专属数据）

### 3. 设置权限（重要）
为每个集合设置权限：
- 点击集合名称进入集合详情
- 点击"权限设置"
- 选择"自定义安全规则"
- 输入以下规则：

```javascript
{
  "read": true,
  "write": true
}
```

**注意**：这是测试用的开放权限，生产环境建议使用更严格的权限控制。

## 第五步：测试应用

### 1. 启动应用
```bash
flutter run
```

### 2. 测试功能
1. **注册测试**：
   - 打开应用，进入注册页面
   - 使用邮箱和密码注册新账号

2. **登录测试**：
   - 使用注册的账号登录

3. **会员功能测试**：
   - 登录后，导航到 `/cloudbase-member` 页面
   - 点击"升级为会员"（测试功能）
   - 测试数据同步功能

## 常见问题

### Q1: 找不到环境ID
**A**: 环境ID在环境首页的"业务指标"区域上方显示，格式类似 `env-xxxxxxxx`

### Q2: 数据库权限错误
**A**: 确保为每个集合都设置了正确的权限规则

### Q3: 应用无法连接
**A**: 检查配置文件中的环境ID是否正确填写

### Q4: 注册失败
**A**: 确保已启用"邮箱登录"

## 下一步

配置完成后，您可以：
1. 测试用户注册和登录功能
2. 体验会员数据同步功能
3. 查看腾讯云开发控制台中的用户数据和使用统计
4. 根据需要调整权限设置和功能配置

## 技术支持

如果遇到问题：
1. 查看 `CLOUDBASE_SETUP_GUIDE.md` 获取详细配置说明
2. 查看腾讯云开发官方文档
3. 检查控制台的错误日志
4. 确认网络连接正常

---

🎉 恭喜！您已经成功配置了腾讯云开发环境！
