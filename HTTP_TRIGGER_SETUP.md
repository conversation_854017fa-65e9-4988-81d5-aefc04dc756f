# 云函数HTTP触发器配置指南

## 问题说明

404错误表示云函数的HTTP触发器没有正确配置。腾讯云开发的云函数需要配置HTTP触发器才能通过HTTP请求调用。

## 配置步骤

### 第一步：为每个云函数配置HTTP触发器

#### 1. 配置注册函数触发器

1. **进入云函数页面**
2. **点击 `auth-signup` 函数**
3. **点击"触发器"选项卡**
4. **点击"新建触发器"**
5. **配置触发器**：
   - 触发器类型：选择"HTTP触发器"
   - 路径：输入 `/auth-signup`
   - 方法：选择 `POST`
   - 启用鉴权：关闭（暂时关闭，后续可开启）
6. **点击"确定"**

#### 2. 配置登录函数触发器

1. **点击 `auth-signin` 函数**
2. **新建HTTP触发器**：
   - 路径：`/auth-signin`
   - 方法：`POST`
   - 启用鉴权：关闭

#### 3. 配置用户信息函数触发器

1. **点击 `auth-userinfo` 函数**
2. **新建HTTP触发器**：
   - 路径：`/auth-userinfo`
   - 方法：`POST`
   - 启用鉴权：关闭

#### 4. 配置密码重置函数触发器

1. **点击 `auth-reset-password` 函数**
2. **新建HTTP触发器**：
   - 路径：`/auth-reset-password`
   - 方法：`POST`
   - 启用鉴权：关闭

### 第二步：获取触发器URL

配置完成后，每个触发器都会生成一个URL，格式类似：
```
https://your-env-id.service.tcloudbase.com/auth-signup
https://your-env-id.service.tcloudbase.com/auth-signin
https://your-env-id.service.tcloudbase.com/auth-userinfo
https://your-env-id.service.tcloudbase.com/auth-reset-password
```

### 第三步：测试触发器

#### 使用Postman或curl测试

**测试注册接口**：
```bash
curl -X POST https://your-env-id.service.tcloudbase.com/auth-signup \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "123456",
    "username": "testuser"
  }'
```

**测试登录接口**：
```bash
curl -X POST https://your-env-id.service.tcloudbase.com/auth-signin \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "123456"
  }'
```

#### 在腾讯云开发控制台测试

1. **进入云函数详情页**
2. **点击"测试"按钮**
3. **输入测试数据**：
```json
{
  "email": "<EMAIL>",
  "password": "123456",
  "username": "testuser"
}
```
4. **点击"执行"**

### 第四步：验证配置

配置完成后，您应该能看到：

1. **触发器列表**：每个云函数都有一个HTTP触发器
2. **触发器URL**：每个触发器都有对应的访问URL
3. **测试成功**：通过HTTP请求能正常调用云函数

## 常见问题

### Q1: 找不到"触发器"选项卡
**A**: 确保您在云函数详情页面，而不是云函数列表页面

### Q2: 创建触发器时提示错误
**A**: 检查路径是否以 `/` 开头，方法是否选择正确

### Q3: 触发器创建成功但仍然404
**A**: 等待几分钟让配置生效，或者重新部署云函数

### Q4: 如何查看触发器URL
**A**: 在触发器列表中可以看到完整的访问URL

## 安全建议

### 生产环境配置

1. **启用鉴权**：在生产环境中建议启用触发器鉴权
2. **配置域名白名单**：限制访问来源
3. **添加请求验证**：在云函数中验证请求来源

### 示例鉴权配置

如果启用鉴权，需要在请求头中添加：
```
Authorization: Bearer your-access-token
```

## 完成检查

配置完成后，请确认：

- ✅ 4个云函数都已创建
- ✅ 每个云函数都配置了HTTP触发器
- ✅ 触发器路径正确（/auth-signup, /auth-signin等）
- ✅ 触发器方法设置为POST
- ✅ 可以通过HTTP请求正常调用

完成这些配置后，您的Flutter应用就可以正常调用云函数了！
