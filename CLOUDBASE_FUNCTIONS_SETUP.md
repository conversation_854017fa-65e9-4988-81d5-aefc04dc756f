# 腾讯云开发云函数创建指南

## 概述

由于腾讯云开发的邮箱登录需要通过云函数来实现，我们需要创建几个云函数来处理用户认证功能。

## 第一步：创建用户集合

在腾讯云开发控制台的数据库页面创建一个新集合：

**集合名称**: `users`
**权限设置**: "读取和修改本人数据"

## 第二步：创建云函数

### 1. 用户注册云函数

**函数名称**: `auth-signup`
**运行环境**: `Node.js 16`

```javascript
const tcb = require('@cloudbase/node-sdk');

const app = tcb.init({
  env: tcb.SYMBOL_CURRENT_ENV
});

const auth = app.auth();
const db = app.database();

exports.main = async (event, context) => {
  try {
    const { email, password, username } = event;
    
    if (!email || !password) {
      return {
        code: 400,
        message: '邮箱和密码不能为空'
      };
    }
    
    // 检查用户是否已存在
    const existingUser = await db.collection('users').where({
      email: email
    }).get();
    
    if (existingUser.data.length > 0) {
      return {
        code: 409,
        message: '用户已存在'
      };
    }
    
    // 创建用户记录
    const userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    
    await db.collection('users').add({
      uid: userId,
      email: email,
      username: username || email.split('@')[0],
      password: password, // 注意：生产环境应该加密存储
      isVip: false,
      isAdmin: false,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    return {
      code: 0,
      message: '注册成功',
      data: {
        uid: userId,
        email: email,
        username: username || email.split('@')[0],
        isVip: false,
        isAdmin: false
      }
    };
  } catch (error) {
    console.error('注册失败:', error);
    return {
      code: 500,
      message: error.message || '注册失败'
    };
  }
};
```

### 2. 用户登录云函数

**函数名称**: `auth-signin`
**运行环境**: `Node.js 16`

```javascript
const tcb = require('@cloudbase/node-sdk');

const app = tcb.init({
  env: tcb.SYMBOL_CURRENT_ENV
});

const db = app.database();

exports.main = async (event, context) => {
  try {
    const { email, password } = event;
    
    if (!email || !password) {
      return {
        code: 400,
        message: '邮箱和密码不能为空'
      };
    }
    
    // 查询用户
    const userQuery = await db.collection('users').where({
      email: email,
      password: password // 注意：生产环境应该使用加密验证
    }).get();
    
    if (userQuery.data.length === 0) {
      return {
        code: 401,
        message: '邮箱或密码错误'
      };
    }
    
    const user = userQuery.data[0];
    
    // 更新最后登录时间
    await db.collection('users').doc(user._id).update({
      lastLoginAt: new Date(),
      updatedAt: new Date()
    });
    
    return {
      code: 0,
      message: '登录成功',
      data: {
        refresh_token: 'token_' + user.uid + '_' + Date.now(),
        user: {
          uid: user.uid,
          email: user.email,
          username: user.username,
          isVip: user.isVip,
          isAdmin: user.isAdmin,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        }
      }
    };
  } catch (error) {
    console.error('登录失败:', error);
    return {
      code: 500,
      message: error.message || '登录失败'
    };
  }
};
```

### 3. 获取用户信息云函数

**函数名称**: `auth-userinfo`
**运行环境**: `Node.js 16`

```javascript
const tcb = require('@cloudbase/node-sdk');

const app = tcb.init({
  env: tcb.SYMBOL_CURRENT_ENV
});

const db = app.database();

exports.main = async (event, context) => {
  try {
    const { token } = event;
    
    if (!token) {
      return {
        code: 401,
        message: '未提供访问令牌'
      };
    }
    
    // 从token中提取用户ID（简化版本）
    const parts = token.split('_');
    if (parts.length < 2) {
      return {
        code: 401,
        message: '无效的访问令牌'
      };
    }
    
    const uid = parts[1];
    
    // 查询用户信息
    const userQuery = await db.collection('users').where({
      uid: uid
    }).get();
    
    if (userQuery.data.length === 0) {
      return {
        code: 404,
        message: '用户不存在'
      };
    }
    
    const user = userQuery.data[0];
    
    return {
      code: 0,
      message: '获取用户信息成功',
      data: {
        uid: user.uid,
        email: user.email,
        username: user.username,
        isVip: user.isVip,
        isAdmin: user.isAdmin,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    };
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return {
      code: 500,
      message: error.message || '获取用户信息失败'
    };
  }
};
```

### 4. 密码重置云函数

**函数名称**: `auth-reset-password`
**运行环境**: `Node.js 16`

```javascript
const tcb = require('@cloudbase/node-sdk');

const app = tcb.init({
  env: tcb.SYMBOL_CURRENT_ENV
});

const db = app.database();

exports.main = async (event, context) => {
  try {
    const { email } = event;
    
    if (!email) {
      return {
        code: 400,
        message: '邮箱不能为空'
      };
    }
    
    // 检查用户是否存在
    const userQuery = await db.collection('users').where({
      email: email
    }).get();
    
    if (userQuery.data.length === 0) {
      return {
        code: 404,
        message: '用户不存在'
      };
    }
    
    // 这里应该发送密码重置邮件
    // 由于需要配置邮件服务，这里先返回成功
    
    return {
      code: 0,
      message: '密码重置邮件已发送（功能待完善）'
    };
  } catch (error) {
    console.error('密码重置失败:', error);
    return {
      code: 500,
      message: error.message || '密码重置失败'
    };
  }
};
```

## 第三步：创建云函数的具体步骤

### 在腾讯云开发控制台操作：

1. **进入云函数页面**
   - 点击左侧菜单"云函数"

2. **创建新函数**
   - 点击"新建云函数"
   - 函数名称：输入上面的函数名（如 `auth-signup`）
   - 运行环境：选择 `Node.js 16`
   - 创建方式：选择"空白函数"

3. **编辑函数代码**
   - 将上面对应的代码复制到编辑器中
   - 点击"保存并部署"

4. **重复创建**
   - 按照同样的步骤创建其他3个云函数

## 第四步：测试云函数

创建完成后，您可以在云函数页面测试每个函数：

### 测试注册函数
```json
{
  "email": "<EMAIL>",
  "password": "123456",
  "username": "testuser"
}
```

### 测试登录函数
```json
{
  "email": "<EMAIL>",
  "password": "123456"
}
```

## 注意事项

1. **密码安全**：示例代码中密码是明文存储，生产环境应该使用加密
2. **令牌管理**：示例中的令牌生成比较简单，生产环境应该使用更安全的方式
3. **错误处理**：可以根据需要添加更详细的错误处理
4. **权限控制**：确保数据库权限设置正确

## 完成后

创建完所有云函数后，您的应用就可以正常进行用户注册和登录了！
