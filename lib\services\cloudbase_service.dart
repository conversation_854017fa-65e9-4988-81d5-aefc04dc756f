import 'dart:convert';
import 'dart:math';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:get_storage/get_storage.dart';
import 'package:uuid/uuid.dart';
import 'package:novel_app/config/cloudbase_config.dart';

/// 腾讯云开发基础服务类
/// 负责腾讯云开发的HTTP API调用和基础操作
class CloudBaseService {
  static CloudBaseService? _instance;
  static CloudBaseService get instance => _instance ??= CloudBaseService._();
  
  CloudBaseService._();
  
  late Dio _dio;
  final _storage = GetStorage();
  final _uuid = const Uuid();
  
  bool _isInitialized = false;
  String? _deviceId;
  String? _accessToken;
  
  // 存储键
  static const String _deviceIdKey = 'cloudbase_device_id';
  static const String _accessTokenKey = 'cloudbase_access_token';
  static const String _userInfoKey = 'cloudbase_user_info';
  
  /// 初始化腾讯云开发服务
  Future<bool> initialize() async {
    if (_isInitialized) return true;
    
    try {
      // 初始化Dio
      _dio = Dio(BaseOptions(
        baseUrl: CloudBaseConfig.baseUrl,
        connectTimeout: CloudBaseConfig.requestTimeout,
        receiveTimeout: CloudBaseConfig.requestTimeout,
        sendTimeout: CloudBaseConfig.requestTimeout,
      ));
      
      // 添加拦截器
      _dio.interceptors.add(_createInterceptor());
      
      // 加载设备ID
      _loadDeviceId();
      
      // 加载访问令牌
      _loadAccessToken();
      
      _isInitialized = true;
      debugPrint('腾讯云开发服务初始化成功');
      return true;
    } catch (e) {
      debugPrint('腾讯云开发服务初始化失败: $e');
      return false;
    }
  }
  
  /// 检查是否已初始化
  bool get isInitialized => _isInitialized;
  
  /// 获取设备ID
  String get deviceId => _deviceId ?? '';
  
  /// 获取访问令牌
  String? get accessToken => _accessToken;
  
  /// 检查用户是否已登录
  bool get isLoggedIn => _accessToken != null;
  
  /// 加载设备ID
  void _loadDeviceId() {
    _deviceId = _storage.read(_deviceIdKey);
    if (_deviceId == null) {
      _deviceId = _uuid.v4();
      _storage.write(_deviceIdKey, _deviceId);
    }
  }
  
  /// 加载访问令牌
  void _loadAccessToken() {
    _accessToken = _storage.read(_accessTokenKey);
  }
  
  /// 保存访问令牌
  void _saveAccessToken(String? token) {
    _accessToken = token;
    if (token != null) {
      _storage.write(_accessTokenKey, token);
    } else {
      _storage.remove(_accessTokenKey);
    }
  }
  
  /// 创建请求拦截器
  Interceptor _createInterceptor() {
    return InterceptorsWrapper(
      onRequest: (options, handler) {
        // 添加基础请求头
        options.headers.addAll(CloudBaseConfig.getBasicHeaders());

        // 添加设备ID和请求ID
        options.headers['x-device-id'] = _deviceId;
        options.headers['x-request-id'] = _uuid.v4();

        // 添加访问令牌（如果有）
        if (_accessToken != null) {
          options.headers['x-cloudbase-credentials'] = _accessToken;
        }

        debugPrint('CloudBase请求: ${options.method} ${options.path}');
        handler.next(options);
      },
      onResponse: (response, handler) {
        debugPrint('CloudBase响应: ${response.statusCode} ${response.requestOptions.path}');
        handler.next(response);
      },
      onError: (error, handler) {
        debugPrint('CloudBase错误: ${error.message}');
        _handleError(error);
        handler.next(error);
      },
    );
  }
  
  /// 处理错误
  void _handleError(DioException error) {
    if (error.response?.statusCode == 401) {
      // 访问令牌过期，清除本地令牌
      _saveAccessToken(null);
      debugPrint('访问令牌已过期，已清除本地令牌');
    }
  }
  

  
  /// 用户注册（邮箱密码）
  Future<Map<String, dynamic>?> signUp({
    required String email,
    required String password,
    String? username,
  }) async {
    try {
      final data = {
        'email': email,
        'password': password,
      };

      if (username != null) {
        data['username'] = username;
      }

      final response = await _dio.post(
        CloudBaseConfig.signUpEndpoint,
        data: data,
      );

      // 检查云函数返回结果
      if (response.data['code'] == 0) {
        // 注册成功，保存用户信息
        final userData = response.data['data'];
        _storage.write(_userInfoKey, userData);

        debugPrint('用户注册成功');
        return userData;
      } else {
        debugPrint('用户注册失败: ${response.data['message']}');
        return null;
      }
    } catch (e) {
      debugPrint('用户注册失败: $e');
      return null;
    }
  }
  
  /// 用户登录（邮箱密码）
  Future<Map<String, dynamic>?> signIn({
    required String email,
    required String password,
  }) async {
    try {
      final data = {
        'email': email,
        'password': password,
      };

      final response = await _dio.post(
        CloudBaseConfig.signInEndpoint,
        data: data,
      );

      // 检查云函数返回结果
      if (response.data['code'] == 0) {
        final result = response.data['data'];

        // 保存访问令牌
        if (result['refresh_token'] != null) {
          _saveAccessToken(result['refresh_token']);
        }

        // 保存用户信息
        if (result['user'] != null) {
          _storage.write(_userInfoKey, result['user']);
        }

        debugPrint('用户登录成功');
        return result;
      } else {
        debugPrint('用户登录失败: ${response.data['message']}');
        return null;
      }
    } catch (e) {
      debugPrint('用户登录失败: $e');
      return null;
    }
  }
  
  /// 用户登出
  Future<bool> signOut() async {
    try {
      if (_accessToken != null) {
        await _dio.post(
          CloudBaseConfig.signOutEndpoint,
          data: {'token': _accessToken},
        );
      }
      
      // 清除本地令牌
      _saveAccessToken(null);
      _storage.remove(_userInfoKey);
      
      debugPrint('用户登出成功');
      return true;
    } catch (e) {
      debugPrint('用户登出失败: $e');
      // 即使请求失败，也清除本地令牌
      _saveAccessToken(null);
      _storage.remove(_userInfoKey);
      return false;
    }
  }
  
  /// 获取用户信息
  Future<Map<String, dynamic>?> getUserInfo() async {
    try {
      final data = {
        'token': _accessToken,
      };

      final response = await _dio.post(
        CloudBaseConfig.userInfoEndpoint,
        data: data,
      );

      // 检查云函数返回结果
      if (response.data['code'] == 0) {
        final userData = response.data['data'];

        // 缓存用户信息
        _storage.write(_userInfoKey, userData);

        debugPrint('获取用户信息成功');
        return userData;
      } else {
        debugPrint('获取用户信息失败: ${response.data['message']}');
        return null;
      }
    } catch (e) {
      debugPrint('获取用户信息失败: $e');
      return null;
    }
  }
  
  /// 重置密码（邮箱）
  Future<bool> resetPassword({
    required String email,
  }) async {
    try {
      final data = {
        'email': email,
      };

      await _dio.post(
        CloudBaseConfig.resetPasswordEndpoint,
        data: data,
      );

      debugPrint('密码重置邮件发送成功');
      return true;
    } catch (e) {
      debugPrint('密码重置失败: $e');
      return false;
    }
  }
  
  /// 生成请求ID
  String generateRequestId() {
    return _uuid.v4();
  }

  /// 公共请求方法（供其他服务使用）
  Future<Map<String, dynamic>?> makeRequest(
    String endpoint, {
    Map<String, dynamic>? data,
    String method = 'POST',
  }) async {
    try {
      final response = await _dio.request(
        endpoint,
        data: data,
        options: Options(method: method),
      );
      return response.data;
    } catch (e) {
      debugPrint('请求失败: $e');
      return null;
    }
  }
}
